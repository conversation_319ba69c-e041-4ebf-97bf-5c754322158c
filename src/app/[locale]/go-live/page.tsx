'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';


import { useWallet } from '@solana/wallet-adapter-react';
import { useTranslations } from 'next-intl';
import { useRouter, useParams } from 'next/navigation';
import { LiveStream } from '@/components/streaming/LiveStream';
import * as Broadcast from '@livepeer/react/broadcast';
import { getIngest } from '@livepeer/react/external';
import ClientWalletButton from '@/components/wallet/ClientWalletButton';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CreatorOnboarding } from '@/components/creator/CreatorOnboarding';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { LiveChat } from '@/components/stream/LiveChat';
import { Eye, DollarSign, Copy, ExternalLink } from 'lucide-react';
import { getStoredAuth } from '@/lib/wallet-auth';

interface StreamData {
  stream?: {
    id: string;
    title: string;
    description: string;
    status: string;
    playbackUrl: string;
  };
  streamKey?: string;
  playbackId?: string;
  title?: string;
  id?: string;
  livepeer?: {
    streamKey: string;
    playbackId: string;
  };
}

export default function GoLivePage() {
  // Use a safe translation function that returns the key if the translation is missing
  const tRaw = useTranslations('streaming');
  const t = (key: string) => {
    try {
      return tRaw(key);
    } catch {
      console.warn(`Missing translation key: ${key}`);
      return key;
    }
  };
  const { connected, publicKey, signMessage } = useWallet();
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string || 'en';
  
  // Authentication state (using same pattern as collection page)
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authLoading, setAuthLoading] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  
  // Check if user has completed profile setup
  const [checkingProfile, setCheckingProfile] = useState(true);
  const [hasUsername, setHasUsername] = useState(false);
  const [userProfile, setUserProfile] = useState<{
    id: string;
    wallet_address: string;
    username?: string;
    avatar_url?: string;
    is_creator: boolean;
    created_at: string;
  } | null>(null);
  const [needsCreatorOnboarding, setNeedsCreatorOnboarding] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // State for stream management
  const [streamData, setStreamData] = useState<StreamData | null>(null);
  const [isActuallyLive, setIsActuallyLive] = useState(false);
  const [viewerCount, setViewerCount] = useState(0);
  const [streamStats, setStreamStats] = useState({
    tips: 0,
    totalEarnings: 0
  });
  const [hlsRetryCount, setHlsRetryCount] = useState(0);


  
  // Authentication function (same as collection page)
  const authenticate = useCallback(async () => {
    if (!connected || !publicKey || !signMessage) {
      setAuthError('Wallet not connected');
      return false;
    }

    setAuthLoading(true);
    setAuthError(null);

    try {
      const walletAddress = publicKey.toString();
      const message = `bonkstream authentication ${walletAddress} ${Date.now()}`;
      
      const signature = await signMessage(new TextEncoder().encode(message));
      const signatureBase64 = Buffer.from(signature).toString('base64');
      
      // Store authentication data
      const authData = {
        signature: signatureBase64,
        message,
        walletAddress,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      };
      
      localStorage.setItem('walletAuthSignature', authData.signature);
      localStorage.setItem('walletAuthMessage', authData.message);
      localStorage.setItem('walletAddress', authData.walletAddress);
      localStorage.setItem('walletAuthExpiry', authData.expiresAt.toString());
      
      setIsAuthenticated(true);
      setAuthLoading(false);
      return true;
    } catch (error) {
      console.error('Authentication failed:', error);
      setAuthError('Failed to authenticate wallet');
      setAuthLoading(false);
      return false;
    }
  }, [connected, publicKey, signMessage]);

  // Check for stored authentication
  const checkStoredAuth = useCallback(() => {
    if (!connected || !publicKey) return false;
    
    const signature = localStorage.getItem('walletAuthSignature');
    const message = localStorage.getItem('walletAuthMessage');
    const address = localStorage.getItem('walletAddress');
    const expiry = localStorage.getItem('walletAuthExpiry');
    
    if (!signature || !message || !address || !expiry) return false;
    if (address !== publicKey.toString()) return false;
    if (Date.now() > parseInt(expiry)) return false;
    
    return true;
  }, [connected, publicKey]);

  // Trigger authentication when wallet connects
  useEffect(() => {
    if (connected && publicKey && !isAuthenticated && !authLoading) {
      if (checkStoredAuth()) {
        setIsAuthenticated(true);
      } else {
        authenticate();
      }
    }
  }, [connected, publicKey, isAuthenticated, authLoading, authenticate, checkStoredAuth]);
  
  // Check user profile and authentication
  useEffect(() => {
    if (!connected || !publicKey || !isAuthenticated) {
      setCheckingProfile(false);
      return;
    }
    
    const checkUserProfile = async () => {
      setCheckingProfile(true);
      
      try {
        const walletAddress = publicKey.toString();
        
        // Create authentication headers
        const signature = localStorage.getItem('walletAuthSignature');
        const message = localStorage.getItem('walletAuthMessage');
        
        const headers: Record<string, string> = {
          'Content-Type': 'application/json'
        };
        
        // Add auth headers if available
        if (signature && message) {
          headers['Authorization'] = `Wallet ${walletAddress}`;
          headers['x-wallet-signature'] = signature;
          headers['x-wallet-message'] = message;
        }
        
        // Fetch profile to check if username is set and if user is creator
        const response = await fetch(`/${locale}/api/profile?wallet=${walletAddress}`, { 
          cache: 'no-cache',
          headers
        });
        
        if (!response.ok) {
          setNeedsCreatorOnboarding(true);
          setCheckingProfile(false);
          return;
        }
        
        const data = await response.json();
        
        if (!data.profile) {
          setNeedsCreatorOnboarding(true);
          setCheckingProfile(false);
          return;
        }
        
        setUserProfile(data.profile);
        
        // Check if user is a creator
        if (!data.profile.is_creator) {
          setNeedsCreatorOnboarding(true);
          setCheckingProfile(false);
          return;
        }
        
        // User is a creator, check if username is set
        if (!data.profile.username) {
          setNeedsCreatorOnboarding(true);
          setCheckingProfile(false);
          return;
        }
        
        // Profile is complete, allow access to go-live
        setHasUsername(true);
        
      } catch (error) {
        console.error('Error checking profile:', error);
        setNeedsCreatorOnboarding(true);
      } finally {
        setCheckingProfile(false);
      }
    };
    
    checkUserProfile();
  }, [connected, publicKey, isAuthenticated, locale]);

  const handleCreatorOnboardingComplete = (updatedProfile: {
    id: string;
    wallet_address: string;
    username?: string;
    avatar_url?: string;
    is_creator: boolean;
    created_at: string;
  }) => {
    setUserProfile(updatedProfile);
    setNeedsCreatorOnboarding(false);
    
    // If the updated profile has a username, allow go-live immediately
    if (updatedProfile.username) {
      setHasUsername(true);
    } else {
      // If no username yet, keep showing onboarding until complete
    }
  };

  const handleStreamStart = (streamData: StreamData) => {
    setStreamData(streamData);
    // Don't switch interface yet - wait for actual broadcasting to start
    
    // Start monitoring stream stats
    const streamId = streamData?.stream?.id || streamData?.id;
    if (streamId) {
      startStatusMonitoring(streamId);
    }
  };

  // New function to handle when broadcasting actually starts
  const handleBroadcastStart = async () => {
    setIsActuallyLive(true); // Now switch to management interface
    localStorage.setItem('bonkstream_broadcasting', 'true'); // Signal to header
    // Dispatch custom event for immediate header update
    window.dispatchEvent(new Event('bonkstream_broadcasting_changed'));

    // Update database to mark stream as actually live
    const streamId = streamData?.stream?.id || streamData?.id;
    if (streamId && publicKey && signMessage) {
      try {
        // Get wallet auth
        const auth = getStoredAuth(publicKey.toString());
        if (!auth) {
          alert('❌ No wallet auth available for database update');
          return;
        }

        const response = await fetch(`/${locale}/api/streams/${streamId}/status`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Wallet ${auth.walletAddress}`,
            'x-wallet-signature': auth.signature,
            'x-wallet-message': auth.message,
          },
          body: JSON.stringify({
            status: 'live',
            isActive: true
          }),
        });

        if (!response.ok) {
          alert('❌ Failed to update stream live status in database');
        } else {
        }
      } catch (error) {
        alert('❌ Error updating stream live status: ' + error);
      }
    }
  };
  
  const handleStopStream = () => {
    setStreamData(null);
    setIsActuallyLive(false);
    localStorage.removeItem('bonkstream_broadcasting'); // Clear broadcasting flag
    // Dispatch custom event for immediate header update
    window.dispatchEvent(new Event('bonkstream_broadcasting_changed'));
    setViewerCount(0);
    setStreamStats({
      tips: 0,
      totalEarnings: 0
    });
    setHlsRetryCount(0);
  };

  // Monitor stream status and stats
  const startStatusMonitoring = (streamId: string) => {
    const interval = setInterval(async () => {
      try {
        // Get comprehensive live stats from the new endpoint
        const statsResponse = await fetch(`/${locale}/api/streams/${streamId}?stats=live`);
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          if (statsData.success && statsData.liveStats) {
            const newViewerCount = statsData.liveStats.viewerCount || 0;
            setViewerCount(newViewerCount);
            setStreamStats(prev => ({
              ...prev,
              tips: statsData.liveStats.tipCount || 0,
              totalEarnings: statsData.liveStats.totalEarnings || 0
            }));
            
            // If stream is no longer live, stop monitoring
            if (!statsData.liveStats.isLive) {
              clearInterval(interval);
              handleStopStream();
            }
          }
        } else {
          console.warn('Failed to fetch live stats:', statsResponse.status);
        }
      } catch (error) {
        console.error('Error monitoring stream:', error);
      }
    }, 10000); // Check every 10 seconds

    return () => {
      clearInterval(interval);
    };
  };

  // Handle ending the stream with loading state and toast
  const [isEndingStream, setIsEndingStream] = useState(false);
  
  const handleEndStream = async () => {
    const streamId = streamData?.stream?.id || streamData?.id;
    if (!streamId || !connected || !publicKey || isEndingStream) return;

    const confirmEnd = window.confirm('Are you sure you want to end this stream? This action cannot be undone.');
    if (!confirmEnd) return;

    setIsEndingStream(true);

    try {
      // Simple API call to end stream
      const response = await fetch(`/${locale}/api/streams/${streamId}/end`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-wallet-address': publicKey.toString(),
        },
        body: JSON.stringify({
          wallet_address: publicKey.toString(),
        }),
      });

      if (response.ok) {
        // Reset state before reload
        setIsActuallyLive(false);
        localStorage.removeItem('bonkstream_broadcasting'); // Clear broadcasting flag
        // Dispatch custom event for immediate header update
        window.dispatchEvent(new Event('bonkstream_broadcasting_changed'));
        setStreamData(null);
        
        // Show success message and reload
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        const errorData = await response.json();
        setIsEndingStream(false);
        console.error('Failed to end stream:', errorData.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error ending stream:', error);
      setIsEndingStream(false);
    }
  };

  // Copy stream link to clipboard
  const copyStreamLink = () => {
    const streamId = streamData?.stream?.id || streamData?.id;
    if (streamId) {
      const shareUrl = `${window.location.origin}/stream/${streamId}`;
      navigator.clipboard.writeText(shareUrl);
      alert('Stream link copied to clipboard!');
    }
  };

  // Initialize HLS.js for live preview with retry mechanism
  useEffect(() => {
    if (!streamData || !isActuallyLive || !videoRef.current) return;

    const playbackId = streamData?.livepeer?.playbackId || streamData?.playbackId;
    if (!playbackId) {
      return;
    }

    const video = videoRef.current;
    const hlsUrl = `https://livepeercdn.studio/hls/${playbackId}/index.m3u8`;
    

    const maxRetries = 10;
    let retryInterval: NodeJS.Timeout;
    setHlsRetryCount(0);

    const initializeHLS = () => {
      // Dynamic import of HLS.js
      import('hls.js').then((HlsModule) => {
        const Hls = HlsModule.default;
        
        if (Hls.isSupported()) {
          const hls = new Hls({
            enableWorker: false,
            lowLatencyMode: true,
            liveDurationInfinity: true,
            manifestLoadingTimeOut: 10000,
            manifestLoadingMaxRetry: 3
          });
          
          hls.loadSource(hlsUrl);
          hls.attachMedia(video);
          
          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            video.play().catch(console.error);
            // Clear retry interval on success
            if (retryInterval) clearInterval(retryInterval);
          });

          hls.on(Hls.Events.ERROR, (event, data) => {
            console.error('HLS error:', event, data);
            
            // Check if it's a "Stream open failed" or "no levels found" error
            if (data.details === 'manifestParsingError' || 
                data.reason === 'no levels found in manifest' ||
                (data.response && data.response.data && typeof data.response.data === 'string' && data.response.data.includes('Stream open failed'))) {
              
              hls.destroy();
              
              if (hlsRetryCount < maxRetries) {
                setHlsRetryCount(prev => prev + 1);
                retryInterval = setTimeout(() => {
                  initializeHLS();
                }, 3000);
              } else {
                console.error('Max retries reached. Stream may not be broadcasting yet.');
              }
            } else {
              console.error('Fatal HLS error, not retrying:', data);
            }
          });

          return () => {
            hls.destroy();
            if (retryInterval) clearInterval(retryInterval);
          };
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          video.src = hlsUrl;
          video.play().catch(console.error);
        } else {
          console.error('HLS is not supported in this browser');
        }
      }).catch((error) => {
        console.error('Failed to load HLS.js:', error);
        // Fallback to native video
        if (videoRef.current?.canPlayType('application/vnd.apple.mpegurl')) {
          videoRef.current.src = hlsUrl;
          videoRef.current.play().catch(console.error);
        }
      });
    };

    // Start the initialization process
    initializeHLS();

    // Cleanup function
    return () => {
      if (retryInterval) clearInterval(retryInterval);
    };
  }, [streamData, isActuallyLive, hlsRetryCount]);

  // Show loading while checking profile
  if (checkingProfile) {
    return (
      <div className="container mx-auto py-8 px-4 min-h-screen bg-bonk-gradient-bg text-white relative">
        <div className="flex flex-col items-center justify-center p-8 bonk-widget rounded-lg space-y-4 max-w-md mx-auto">
          <LoadingSpinner size="md" />
          <h2 className="bonk-header text-2xl mt-4">Checking Profile...</h2>
          <p className="bonk-body text-center text-white/60">
            Verifying your setup before you can go live
          </p>
        </div>
      </div>
    );
  }

  // Not connected state
  if (!connected) {
    return (
      <div className="container mx-auto py-8 px-4 min-h-screen bg-bonk-gradient-bg text-white relative">
        <div className="flex flex-col items-center justify-center p-8 bonk-widget rounded-lg space-y-4 max-w-md mx-auto">
          <h2 className="bonk-header text-2xl">{t('connectWalletFirst')}</h2>
          <p className="bonk-body text-center text-white/60 mb-4">
            {t('walletRequiredForStreaming')}
          </p>
          <ClientWalletButton />
        </div>
      </div>
    );
  }

  // Authentication loading state
  if (authLoading || (!isAuthenticated && connected)) {
    return (
      <div className="container mx-auto py-8 px-4 min-h-screen bg-bonk-gradient-bg text-white relative">
        <div className="flex flex-col items-center justify-center p-8 bonk-widget rounded-lg space-y-4 max-w-md mx-auto">
          <LoadingSpinner size="md" />
          <h2 className="bonk-header text-2xl mt-4">Authenticating...</h2>
          <p className="bonk-body text-center text-white/60">
            Please sign the message in your wallet to access go-live
          </p>
        </div>
      </div>
    );
  }

  // Authentication error state
  if (authError) {
    return (
      <div className="container mx-auto py-8 px-4 min-h-screen bg-bonk-gradient-bg text-white relative">
        <div className="flex flex-col items-center justify-center p-8 bonk-widget rounded-lg space-y-4 max-w-md mx-auto border border-red-500/30">
          <h2 className="bonk-header text-2xl text-red-400">Authentication Failed</h2>
          <p className="bonk-body text-center text-white/60 mb-4">
            {authError}
          </p>
          <div className="flex gap-2">
            <Button onClick={() => authenticate()} className="bonk-btn bg-bonk-orange hover:bg-bonk-orange/80 text-white">
              Try Again
            </Button>
            <Button onClick={() => window.location.reload()} className="bonk-btn bg-bonk-widget-black hover:bg-bonk-widget-dark text-white">
              Refresh Page
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show creator onboarding wizard for non-creator users
  if (needsCreatorOnboarding && userProfile) {
    return (
      <div className="container mx-auto py-8 px-4 min-h-screen bg-bonk-gradient-bg text-white relative">
        <CreatorOnboarding 
          profile={userProfile}
          locale={locale}
          onOnboardingComplete={handleCreatorOnboardingComplete}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 min-h-screen bg-bonk-gradient-bg text-white relative">
      {/* Stream ending overlay */}
      {isEndingStream && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-bonk-widget border border-bonk-orange/30 rounded-lg p-8 text-center max-w-md mx-4">
            <LoadingSpinner size="lg" />
            <h3 className="text-xl font-bold mb-2 mt-4">Ending Stream...</h3>
            <p className="text-white/60 mb-4">
              Please wait while we end your stream safely.
            </p>
            <p className="text-xs text-white/40">
              Do not close this tab or refresh the page.
            </p>
          </div>
        </div>
      )}
      
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <h1 className="bonk-header text-3xl">
          {isActuallyLive ? 'Live Streaming' : t('goLive')}
        </h1>
        {isActuallyLive && streamData && (
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-red-400 font-medium">LIVE</span>
            <span className="text-white/60">•</span>
            <span className="text-white/80">{streamData.stream?.title || streamData.title || 'Live Stream'}</span>
          </div>
        )}
        {isActuallyLive ? (
          <Button
            onClick={handleEndStream}
            variant="destructive"
            className="bg-red-600 hover:bg-red-700 text-white"
            disabled={isEndingStream}
          >
            {isEndingStream ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
                <span>Ending Stream...</span>
              </div>
            ) : (
              'End Stream'
            )}
          </Button>
        ) : (
          <Button
            variant="outline" 
            onClick={() => router.back()}
            className="bonk-btn border-bonk-orange/30 text-white hover:bg-bonk-widget-dark"
          >
            {t('back')}
          </Button>
        )}
      </div>

      {/* Main Content */}
      {hasUsername && !isActuallyLive ? (
        <div className="space-y-6">
          <LiveStream
            onStreamStart={handleStreamStart}
            onStopStream={handleStopStream}
            onBroadcastStart={handleBroadcastStart}
          />
        </div>
      ) : hasUsername && isActuallyLive ? (
        <div className="space-y-6">
          {/* Quick Live Notice - Compact */}
          <div className="flex items-center gap-3 p-4 bg-red-900/20 border border-red-500/50 rounded-lg">
            <div className="text-red-400 text-xl">🔴</div>
            <div>
              <span className="font-bold text-red-400">You are LIVE!</span>
              <span className="text-sm text-white/80 ml-2">
                DO NOT close this window or your stream will end.
              </span>
            </div>
          </div>

          {/* Live Stream Preview */}
          <Card className="bonk-widget border-bonk-orange/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                Live Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
                {(streamData?.streamKey || streamData?.livepeer?.streamKey) ? (
                  <div className="relative w-full h-full">
                    {/* Visible indicator that Broadcast is rendering */}
                    <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs z-10">
                      BROADCAST ACTIVE
                    </div>

                    <Broadcast.Root
                      ingestUrl={getIngest(streamData?.streamKey || streamData?.livepeer?.streamKey)}
                      aspectRatio={16 / 9}
                      forceEnabled={true}
                    >
                      <Broadcast.Video
                        className="w-full h-full object-cover"
                        title="Live Stream"
                        playsInline={true}
                        muted={true}
                        autoPlay={true}
                      onLoadedData={() => {
                        // NOW call handleBroadcastStart to switch to live interface
                        try {
                          handleBroadcastStart();
                        } catch (error) {
                          alert('❌ handleBroadcastStart failed: ' + error);
                        }
                      }}
                      onError={(error) => {
                        alert('❌ Broadcast video error: ' + JSON.stringify(error));
                      }}
                      onLoadStart={() => {
                      }}
                      onCanPlay={() => {
                      }}
                      onPlay={() => {
                      }}
                      onWaiting={() => {
                      }}
                    />
                    
                    {/* LOADING OVERLAY REMOVED - WAS HIDING THE VIDEO */}
                  </Broadcast.Root>
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-16 h-16 mx-auto mb-4 animate-spin rounded-full border-4 border-bonk-orange/30 border-t-bonk-orange"></div>
                      <h3 className="text-lg font-medium text-white mb-2">No Stream Key Found</h3>
                      <p className="text-white/60 text-sm">
                        Stream key is missing. Check debug info above.
                      </p>
                    </div>
                  </div>
                )}
                
                {/* Live indicator overlay */}
                <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                  LIVE
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Live Chat - Below Video */}
          {(streamData?.stream?.id || streamData?.id) && (
            <LiveChat
              streamId={streamData?.stream?.id || streamData?.id || ''}
              isCreator={true}
              className="mt-6"
            />
          )}

          {/* Fallback message when stream ID is missing */}
          {isActuallyLive && !(streamData?.stream?.id || streamData?.id) && (
            <div className="mt-6 p-4 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
              <h3 className="text-yellow-400 font-bold mb-2">⚠️ Chat Temporarily Unavailable</h3>
              <p className="text-white/80 text-sm mb-2">Live chat is not available because the stream ID could not be retrieved. This is a technical issue that should be resolved by refreshing the page.</p>
              <div className="flex gap-2 mt-4">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm"
                >
                  Refresh Page
                </button>
              </div>
            </div>
          )}

          {/* Stats Cards - After Chat */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="bonk-widget border-bonk-orange/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white/60 flex items-center">
                  <Eye className="w-4 h-4 mr-2" />
                  Viewers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{viewerCount}</div>
                <p className="text-xs text-white/60">Currently watching</p>
              </CardContent>
            </Card>

            <Card className="bonk-widget border-bonk-orange/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white/60 flex items-center">
                  <DollarSign className="w-4 h-4 mr-2" />
                  Tips
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{streamStats.tips}</div>
                <p className="text-xs text-white/60">Total tips received</p>
              </CardContent>
            </Card>

            <Card className="bonk-widget border-bonk-orange/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white/60 flex items-center">
                  <DollarSign className="w-4 h-4 mr-2" />
                  Earnings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{streamStats.totalEarnings.toLocaleString()}</div>
                <p className="text-xs text-white/60">BONK earned</p>
              </CardContent>
            </Card>
          </div>

          {/* Stream Actions - Full Width */}
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Share Stream Section */}
              <Card className="bonk-widget border-bonk-orange/20">
                <CardHeader>
                  <CardTitle className="text-white">Share Stream</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <label className="text-sm font-medium text-white/60">Stream Link</label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        readOnly
                        value={streamData ? `${window.location.origin}/stream/${streamData.stream?.id || streamData.id || ''}` : ''}
                        className="flex-1 px-3 py-2 bg-bonk-widget-dark border border-bonk-orange/20 rounded text-white text-sm"
                      />
                      <Button
                        onClick={copyStreamLink}
                        size="sm"
                        className="bonk-btn"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={() => {
                          const streamId = streamData?.stream?.id || streamData?.id;
                          if (streamId) window.open(`/stream/${streamId}`, '_blank');
                        }}
                        size="sm"
                        className="bonk-btn"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* End Stream Section */}
              <Card className="bonk-widget border-red-500/30 bg-red-900/20">
                <CardHeader>
                  <CardTitle className="text-red-400">End Stream</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-white/60">
                    This will permanently end your live stream. Viewers will no longer be able to watch or tip.
                  </p>
                  <Button
                    onClick={handleEndStream}
                    className="w-full bg-red-600 hover:bg-red-700 text-white"
                    disabled={isEndingStream}
                  >
                    {isEndingStream ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-4 h-4 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
                        <span>Ending Stream...</span>
                      </div>
                    ) : (
                      'End Stream'
                    )}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center p-8 bonk-widget rounded-lg space-y-4 max-w-md mx-auto">
          <div className="w-12 h-12 animate-spin rounded-full border-4 border-bonk-orange/30 border-t-bonk-orange"></div>
          <h2 className="bonk-header text-2xl">Setting up your profile...</h2>
          <p className="bonk-body text-center text-white/60">
            Please complete your creator setup to start streaming
          </p>
        </div>
      )}
    </div>
  );
}
